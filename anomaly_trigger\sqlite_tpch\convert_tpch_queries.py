#!/usr/bin/env python3
"""
简洁的TPC-H查询转换工具
将tpch-queries目录下的PostgreSQL查询转换为SQLite格式
"""

import os
import glob
from pathlib import Path

try:
    import sqlglot
except ImportError:
    print("错误: 需要安装sqlglot库")
    print("请运行: pip install sqlglot")
    exit(1)


def convert_sql_file(input_file, output_file):
    """转换单个SQL文件从PostgreSQL到SQLite"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除TPC-H特有的参数标记行
        lines = content.split('\n')
        sql_lines = []
        
        for line in lines:
            # 跳过TPC-H参数标记行
            if line.strip().startswith(':') and len(line.strip()) <= 10:
                continue
            sql_lines.append(line)
        
        sql_content = '\n'.join(sql_lines)
        
        # 使用sqlglot转换
        try:
            converted = sqlglot.transpile(sql_content, read="postgres", write="sqlite")[0]
        except Exception as e:
            print(f"警告: {input_file} 转换失败: {e}")
            # 如果转换失败，保留原始内容
            converted = sql_content
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted)
        
        print(f"已转换: {input_file} -> {output_file}")
        
    except Exception as e:
        print(f"错误处理文件 {input_file}: {e}")


def main():
    """主函数"""
    # 设置路径
    script_dir = Path(__file__).parent
    tpch_queries_dir = script_dir.parent / "tpch-queries"
    output_dir = script_dir
    
    if not tpch_queries_dir.exists():
        print(f"错误: 找不到tpch-queries目录: {tpch_queries_dir}")
        return
    
    # 查找所有非explain的SQL文件
    sql_files = []
    for file_path in tpch_queries_dir.glob("*.sql"):
        if not file_path.name.endswith(".explain.sql"):
            sql_files.append(file_path)
    
    if not sql_files:
        print("未找到需要转换的SQL文件")
        return
    
    print(f"找到 {len(sql_files)} 个SQL文件需要转换")
    
    # 转换每个文件
    for sql_file in sorted(sql_files):
        output_file = output_dir / f"sqlite_{sql_file.name}"
        convert_sql_file(sql_file, output_file)
    
    print(f"\n转换完成! 输出文件保存在: {output_dir}")


if __name__ == "__main__":
    main()
