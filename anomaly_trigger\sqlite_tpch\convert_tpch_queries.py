#!/usr/bin/env python3
"""
简洁的TPC-H查询转换工具
将tpch-queries目录下的PostgreSQL查询转换为SQLite格式
"""

import os
import glob
from pathlib import Path

try:
    import sqlglot
except ImportError:
    print("错误: 需要安装sqlglot库")
    print("请运行: pip install sqlglot")
    exit(1)


def clean_sql_content(content):
    """清理SQL内容，移除TPC-H特有的注释和符号，并处理参数占位符"""
    import re

    lines = content.split('\n')
    cleaned_lines = []

    for line in lines:
        stripped = line.strip()

        # 跳过空行
        if not stripped:
            continue

        # 跳过TPC-H注释行（以--开头的注释）
        if stripped.startswith('--'):
            continue

        # 跳过TPC-H参数标记行（如:x, :o, :n -1等）
        if stripped.startswith(':') and len(stripped) <= 10:
            continue

        # 跳过包含$ID$的行
        if '$ID$' in stripped:
            continue

        # 处理TPC-H参数占位符，替换为示例值
        processed_line = line

        # 替换日期参数 ':1' -> '1995-01-01'
        processed_line = re.sub(r"date\s+':1'", "date('1995-01-01')", processed_line)
        processed_line = re.sub(r"':1'", "'1995-01-01'", processed_line)

        # 替换数值参数
        processed_line = re.sub(r':1\b', '15', processed_line)  # 示例数值
        processed_line = re.sub(r':2\b', '0.05', processed_line)  # 示例折扣
        processed_line = re.sub(r':3\b', '24', processed_line)  # 示例数量
        processed_line = re.sub(r':4\b', '10', processed_line)
        processed_line = re.sub(r':5\b', '20', processed_line)
        processed_line = re.sub(r':6\b', '30', processed_line)
        processed_line = re.sub(r':7\b', '40', processed_line)
        processed_line = re.sub(r':8\b', '50', processed_line)
        processed_line = re.sub(r':9\b', '60', processed_line)
        processed_line = re.sub(r':10\b', '70', processed_line)

        # 替换字符串参数
        processed_line = re.sub(r"'%:2'", "'%STEEL'", processed_line)
        processed_line = re.sub(r"':2%'", "'STEEL%'", processed_line)
        processed_line = re.sub(r"':2'", "'STEEL'", processed_line)
        processed_line = re.sub(r"':3'", "'ASIA'", processed_line)

        # 处理TPC-H特有的视图命名（revenue:s -> revenue_temp）
        processed_line = re.sub(r'revenue:s', 'revenue_temp', processed_line)

        cleaned_lines.append(processed_line)

    return '\n'.join(cleaned_lines)


def convert_sql_file(input_file, output_file):
    """转换单个SQL文件从PostgreSQL到SQLite"""
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 清理SQL内容
        cleaned_content = clean_sql_content(content)

        # 如果清理后内容为空，跳过
        if not cleaned_content.strip():
            print(f"跳过空文件: {input_file}")
            return

        # 使用sqlglot转换
        try:
            converted = sqlglot.transpile(cleaned_content, read="postgres", write="sqlite")[0]
        except Exception as e:
            print(f"警告: {input_file} 转换失败: {e}")
            # 如果转换失败，保留清理后的内容
            converted = cleaned_content

        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted)

        print(f"已转换: {input_file} -> {output_file}")

    except Exception as e:
        print(f"错误处理文件 {input_file}: {e}")


def main():
    """主函数"""
    # 设置路径
    script_dir = Path(__file__).parent
    tpch_queries_dir = script_dir.parent / "tpch-queries"
    output_dir = script_dir
    
    if not tpch_queries_dir.exists():
        print(f"错误: 找不到tpch-queries目录: {tpch_queries_dir}")
        return
    
    # 查找所有非explain的SQL文件
    sql_files = []
    for file_path in tpch_queries_dir.glob("*.sql"):
        if not file_path.name.endswith(".explain.sql"):
            sql_files.append(file_path)
    
    if not sql_files:
        print("未找到需要转换的SQL文件")
        return
    
    print(f"找到 {len(sql_files)} 个SQL文件需要转换")
    
    # 转换每个文件
    for sql_file in sorted(sql_files):
        output_file = output_dir / f"sqlite_{sql_file.name}"
        convert_sql_file(sql_file, output_file)
    
    print(f"\n转换完成! 输出文件保存在: {output_dir}")


if __name__ == "__main__":
    main()
