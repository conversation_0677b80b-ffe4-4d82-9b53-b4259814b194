/* $ID$ */ /* TPC-H/TPC-R Customer Distribution Query (Q13) */ /* Functional Query Definition */ /* Approved February 1998 */ SELECT c_count, COUNT(*) AS custdist FROM (SELECT c_custkey, COUNT(o_orderkey) FROM customer LEFT OUTER JOIN orders ON c_custkey = o_custkey AND NOT o_comment LIKE '%:1%:2%' GROUP BY c_custkey) AS c_orders GROUP BY c_count ORDER BY custdist DESC NULLS FIRST, c_count DESC NULLS FIRST