/* $ID$ */ /* TPC-H/TPC-R Suppliers Who Kept Orders Waiting Query (Q21) */ /* Functional Query Definition */ /* Approved February 1998 */ SELECT s_name, COUNT(*) AS numwait FROM supplier, lineitem AS l1, orders, nation WHERE s_suppkey = l1.l_suppkey AND o_orderkey = l1.l_orderkey AND o_orderstatus = 'F' AND l1.l_receiptdate > l1.l_commitdate AND EXISTS(SELECT * FROM lineitem AS l2 WHERE l2.l_orderkey = l1.l_orderkey AND l2.l_suppkey <> l1.l_suppkey) AND NOT EXISTS(SELECT * FROM lineitem AS l3 WHERE l3.l_orderkey = l1.l_orderkey AND l3.l_suppkey <> l1.l_suppkey AND l3.l_receiptdate > l3.l_commitdate) AND s_nationkey = n_nationkey AND n_name = ':1' GROUP BY s_name ORDER BY numwait DESC NULLS FIRST, s_name NULLS LAST