/* $ID$ */ /* TPC-H/TPC-R Shipping Priority Query (Q3) */ /* Functional Query Definition */ /* Approved February 1998 */ SELECT l_orderkey, SUM(l_extendedprice * (1 - l_discount)) AS revenue, o_orderdate, o_shippriority FROM customer, orders, lineitem WHERE c_mktsegment = ':1' AND c_custkey = o_custkey AND l_orderkey = o_orderkey AND o_orderdate < DATE(':2') AND l_shipdate > DATE(':2') GROUP BY l_orderkey, o_orderdate, o_shippriority ORDER BY revenue DESC NULLS FIRST, o_orderdate NULLS LAST