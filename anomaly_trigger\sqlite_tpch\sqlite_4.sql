/* $ID$ */ /* TPC-H/TPC-R Order Priority Checking Query (Q4) */ /* Functional Query Definition */ /* Approved February 1998 */ SELECT o_orderpriority, COUNT(*) AS order_count FROM orders WHERE o_orderdate >= DATE(':1') AND o_orderdate < DATE(':1') + INTERVAL '3' MONTH AND EXISTS(SELECT * FROM lineitem WHERE l_orderkey = o_orderkey AND l_commitdate < l_receiptdate) GROUP BY o_orderpriority ORDER BY o_orderpriority NULLS LAST