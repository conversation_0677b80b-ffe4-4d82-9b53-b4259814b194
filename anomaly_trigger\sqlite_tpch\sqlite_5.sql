/* $ID$ */ /* TPC-H/TPC-R Local Supplier Volume Query (Q5) */ /* Functional Query Definition */ /* Approved February 1998 */ SELECT n_name, SUM(l_extendedprice * (1 - l_discount)) AS revenue FROM customer, orders, lineitem, supplier, nation, region WHERE c_custkey = o_custkey AND l_orderkey = o_orderkey AND l_suppkey = s_suppkey AND c_nationkey = s_nationkey AND s_nationkey = n_nationkey AND n_regionkey = r_regionkey AND r_name = ':1' AND o_orderdate >= DATE(':2') AND o_orderdate < DATE(':2') + INTERVAL '1' YEAR GROUP BY n_name ORDER BY revenue DESC NULLS FIRST